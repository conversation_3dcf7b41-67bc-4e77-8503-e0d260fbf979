# MOSCI Offender ID Duplication Bug Fix - Test Plan

## Bug Description
When saving MOSCI data with empty rows, the controller was receiving duplicate offender IDs where empty rows inherited the offender ID from populated rows.

## Root Cause
JavaScript was sending both serialized form data (with duplicated IDs from model binding) AND clean JSON data, causing conflicts in the controller.

## Solution Implemented

### 1. JavaScript Changes
- Removed `form.serialize()` that was causing duplication
- Send only essential form fields + clean JSON data
- Include anti-forgery token for security

### 2. Controller Changes  
- Enhanced JSON processing with proper null-safety
- Clear and rebuild inmates collection with only valid JSON data
- Prevent processing of empty/invalid rows

## Test Steps

### Test Case 1: Basic Save with Empty Rows
1. Navigate to MOSCI screen
2. Search for a valid unscheduled inmate (e.g., "A123456")
3. Click "Add New Inmate" button twice to create 2 empty rows
4. Leave the empty rows completely blank
5. Click Save button
6. **Expected:** Only 1 inmate should be processed, no duplication errors

### Test Case 2: Mixed Valid and Empty Rows
1. Navigate to MOSCI screen
2. Search for first inmate (e.g., "A123456") - populates row 1
3. <PERSON>lick "Add New Inmate" to create row 2
4. Fill row 2 with different valid inmate data (e.g., "A789012")
5. Click "Add New Inmate" to create row 3
6. Leave row 3 empty
7. Click Save button
8. **Expected:** Only 2 inmates should be processed, row 3 ignored

### Test Case 3: All Empty Rows
1. Navigate to MOSCI screen
2. Click "Add New Inmate" button twice
3. Leave all rows empty
4. Click Save button
5. **Expected:** "No inmate data found to save" error message

## Verification Points

### Browser Console Logs
- Check that `gridData` array contains only rows with valid offender IDs
- Verify form data doesn't include serialized grid fields

### Server-Side Logs
- Controller should receive only valid inmates in model.Inmates collection
- No duplicate offender IDs should appear in processing

### Database
- Only valid inmates should be inserted/updated
- No orphaned or duplicate records

## Success Criteria
✅ Empty rows do not inherit offender IDs from populated rows
✅ Only rows with valid data are processed by the controller
✅ Save operation completes successfully without duplication errors
✅ Database contains only the expected valid records
