# MOSCI Enhanced Save Validation - Test Plan

## Overview
Enhanced the MOSCI save functionality to provide comprehensive validation that prevents saving when there are incomplete rows, ensuring consistent validation on every save attempt.

## Problem Solved
- **Issue**: Users could bypass validation on subsequent save attempts after receiving "Incomplete data in row" message
- **Root Cause**: Client-side validation was insufficient and server-side validation logic had a flaw
- **Solution**: Added comprehensive client-side validation and fixed server-side validation logic

## Changes Made

### 1. Client-Side Validation Enhancement
**File:** `MVC/Areas/Transfers/Views/Mosci/Index.cshtml`
**Location:** Lines 2661-2766

#### New Validation Logic:
- **Comprehensive Field Validation**: Checks all required fields for rows with Offender ID
- **Required Fields**: InmateIdPrefix, LastName, FirstName, ToInstitution, ScheduleDate
- **User-Friendly Messages**: Specific error messages indicating which fields are missing
- **Consistent Validation**: Runs on every save attempt, not just the first one

#### Validation Flow:
1. Check if at least one row has meaningful data (Offender ID)
2. For each row with Offender ID, validate all required fields
3. Collect incomplete row numbers
4. Show detailed error message if any incomplete rows found
5. Prevent form submission until all rows are complete

### 2. Server-Side Validation Fix
**File:** `MVC/Areas/Transfers/Controllers/MosciController.cs`
**Location:** Lines 564-600

#### Fixed Issues:
- **Logic Error**: Fixed condition that was always true `((!string.IsNullOrWhiteSpace(inmate.OffenderId) || (string.IsNullOrWhiteSpace(inmate.OffenderId))))`
- **Proper Row Filtering**: Now only validates rows that have an OffenderId
- **Enhanced Error Messages**: More descriptive messages matching client-side validation

## Test Cases

### Test Case 1: Single Incomplete Row
**Steps:**
1. Navigate to MOSCI screen
2. Search for and add a valid inmate (populates some fields)
3. Leave Schedule Date empty
4. Click Save button
5. Click Save button again (second attempt)

**Expected Results:**
- First save attempt: Shows "Incomplete data in row 1. Please fill in all required fields..."
- Second save attempt: Same validation error, save is blocked
- Save only succeeds when all fields are completed

### Test Case 2: Multiple Incomplete Rows
**Steps:**
1. Navigate to MOSCI screen
2. Add 3 inmates with Offender IDs
3. Leave Last Name empty in row 1
4. Leave To Institution empty in row 2
5. Leave Schedule Date empty in row 3
6. Click Save button multiple times

**Expected Results:**
- Shows "Incomplete data in rows 1, 2, 3. Please fill in all required fields..."
- Validation blocks save on every attempt
- Clear indication of which rows need attention

### Test Case 3: Mixed Complete and Incomplete Rows
**Steps:**
1. Navigate to MOSCI screen
2. Add 3 inmates
3. Complete all fields for row 1
4. Leave First Name empty in row 2
5. Complete all fields for row 3
6. Click Save button

**Expected Results:**
- Shows "Incomplete data in row 2. Please fill in all required fields..."
- Only row 2 is flagged as incomplete
- Save is blocked until row 2 is completed

### Test Case 4: All Rows Complete
**Steps:**
1. Navigate to MOSCI screen
2. Add 2 inmates with all required fields filled
3. Click Save button

**Expected Results:**
- No validation errors
- Save proceeds successfully
- Success message displayed

### Test Case 5: Empty Rows (No Offender ID)
**Steps:**
1. Navigate to MOSCI screen
2. Click "Add New Inmate" to create empty rows
3. Leave all fields empty (no Offender ID)
4. Click Save button

**Expected Results:**
- Shows "Please add at least one inmate with an Offender ID..."
- Empty rows are ignored in validation
- No "incomplete data" messages for empty rows

### Test Case 6: Validation Consistency
**Steps:**
1. Navigate to MOSCI screen
2. Add inmate with missing Schedule Date
3. Click Save button (first attempt)
4. Note the error message
5. Click Save button again (second attempt)
6. Note the error message

**Expected Results:**
- Both attempts show identical validation error
- No bypass of validation on subsequent attempts
- Consistent behavior every time

## Validation Rules

### Required Fields (for rows with Offender ID):
1. **InmateIdPrefix**: Must be selected from dropdown
2. **LastName**: Must not be empty or whitespace
3. **FirstName**: Must not be empty or whitespace  
4. **ToInstitution**: Must be selected (not empty or "0")
5. **ScheduleDate**: Must not be empty

### Additional Validations:
- **From/To Institution Conflict**: From and To institutions cannot be the same
- **Duplicate Offenders**: Same prefix+offender ID cannot appear in multiple rows
- **Valid Offender**: Offender ID must exist in the system

## Error Messages

### Client-Side Messages:
- Single row: "Incomplete data in row X. Please fill in all required fields (Prefix, Last Name, First Name, To Institution, and Schedule Date)."
- Multiple rows: "Incomplete data in rows X, Y, Z. Please fill in all required fields (Prefix, Last Name, First Name, To Institution, and Schedule Date) for all rows."

### Server-Side Messages:
- Single row: "Incomplete data in row X. Please fill in all required fields (Prefix, Last Name, First Name, To Institution, and Schedule Date)."
- Multiple rows: "Incomplete data in rows X, Y, Z. Please fill in all required fields (Prefix, Last Name, First Name, To Institution, and Schedule Date) for all rows."

## Technical Implementation

### Client-Side Validation Function:
```javascript
// Comprehensive validation for incomplete data in all rows
var incompleteRows = [];
$('#inmateTable tbody tr').each(function (index) {
    var $row = $(this);
    var offenderId = $row.find('input[id*="OffenderId"]').val();

    // Only validate rows that have an offender ID
    if (offenderId && offenderId.trim() !== '') {
        // Check all required fields and collect incomplete rows
        // Show detailed error message if validation fails
    }
});
```

### Server-Side Validation Logic:
```csharp
// Only validate rows that have an OffenderId
if (!string.IsNullOrWhiteSpace(inmate.OffenderId))
{
    bool hasError = string.IsNullOrWhiteSpace(inmate.InmateIdPrefix) ||
                    string.IsNullOrWhiteSpace(inmate.LastName) ||
                    string.IsNullOrWhiteSpace(inmate.FirstName) ||
                    !inmate.SchdInst.HasValue || inmate.SchdInst.Value <= 0 ||
                    !inmate.Instno.HasValue || inmate.Instno.Value <= 0 ||
                    inmate.SchDate == DateTime.MinValue ||
                    // Check if From and To institutions are the same
                    inmate.Instno.HasValue && inmate.SchdInst.HasValue &&
                    inmate.Instno.Value == inmate.SchdInst.Value;
}
```

## Success Criteria
✅ Client-side validation prevents submission with incomplete data
✅ Server-side validation provides backup validation
✅ Validation runs consistently on every save attempt
✅ Clear, specific error messages guide user to fix issues
✅ No bypass of validation on subsequent attempts
✅ Only rows with Offender ID are validated (empty rows ignored)
✅ All required fields are properly validated
✅ Error messages match between client and server validation
